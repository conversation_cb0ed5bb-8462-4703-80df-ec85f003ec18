<template>
  <div class="image-text-recognition" v-loading="loading" element-loading-text="识别中...">
    <!-- 图片上传和预览区域 -->
    <el-header class="section-header">
      <div class="input-tip"></div>
      <div>选择图片</div>
    </el-header>
    <div style="display: flex; margin-top: 20px">
      <el-upload class="avatar-uploader" :auto-upload="false" :show-file-list="false" :on-change="handleChange">
        <img v-if="imageUrl" :src="imageUrl" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
      </el-upload>
      <el-input type="textarea" v-model="imgText" rows="9" placeholder="输入描述" style="margin-left: 20px; width: 800px" resize="none" />
    </div>

    <div class="upload-footer">
      <!-- 提交按钮 -->
          <!-- <el-divider border-style="dashed" style="position: absolute; top: -70%;left: -140px;width: 800px;"/> -->
      <el-button type="primary" style="width: 95px; height: 42px; margin-left: 420px" @click="submitRecognition('0')" :disabled="!imageUrl || imgText != ''"> OCR识别 </el-button>
      <el-button type="primary" style="width: 95px; height: 42px; margin-left: 40px" @click="mSubmitRecognition('1')" :disabled="!imageUrl || !imgText"> 图片理解 </el-button>
    </div>

    <el-header class="section-header">
      <div class="input-tip" style="margin-top: -130px"></div>
      <div style="margin-top: -130px">输出结果</div>
      <el-button type="primary" plain class="submit-btn" @click="copy">复制内容</el-button>
    </el-header>
    <!-- 结果输出区域 -->
    <!-- 使用 <textarea> 来显示输出 -->
    <el-input type="textarea" v-model="recognizedText" rows="10" placeholder="输出结果" readonly style="display: block; margin-top: -50px; width: 1220px" resize="none" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElLoading, ElMessage } from "element-plus";
import axios from "axios";
import { Plus } from "@element-plus/icons-vue";
import { t } from "@wangeditor/editor";
import app from "@/constants/app";

const fileList = ref<File[]>([]); // 图片上传相关变量
const imageUrl = ref<string>(""); // 用于存储图片的预览地址
const recognizedText = ref(""); // 存储识别结果
const loading = ref(false); // 加载动画
const imgText = ref(""); // 图片理解
// 预览图片实现
const handleChange = (file: { raw: File }) => {
  const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/bmp"];
  if (!allowedTypes.includes(file.raw.type)) {
    ElMessage.error("请选择JPG、PNG、GIF或BMP格式的图片");
    return;
  }

  fileList.value = [file.raw];
  imageUrl.value = URL.createObjectURL(file.raw);
};

// // 清除图片
// const handleRemove = () => {
//   imageUrl.value = '';
//   recognizedText.value = '';
//   uploadMessage.value = '请选择图片';
//   fileList.value = [];
// };
import clipboard from "clipboard";
const copy = () => {
  if (recognizedText.value == "" || recognizedText.value == undefined) {
    ElMessage.warning("请先识别内容");
    return;
  }
  clipboard.copy(recognizedText.value);
  ElMessage.success("复制成功");
};
// 提交识别
const submitRecognition = async (value: string) => {
  if (!fileList.value.length) {
    ElMessage.error("请选择图片");
    return;
  }
  const file = fileList.value[0];

  try {
    const formData = new FormData();
    formData.append("image", file, file.name);
    formData.append("input_text", imgText.value || "请描述图片内容"); // 默认值
    formData.append("mode", value);
    //添加一个加载动画
    loading.value = true;
    const res = await axios.post(app.ocr_api + "/image_api", formData);

    // 假设服务器返回的数据结构是 JSON 对象
    if (res.data.error) {
      ElMessage.error(`识别失败: ${res.data.error}`);
      loading.value = false;
      return;
    }

    recognizedText.value = res.data.recognized_text;
    ElMessage.success("识别成功");
    loading.value = false;
    console.log("Response from server:", res.data);
    console.log("recognizedText:", recognizedText.value);
  } catch (error) {
    ElMessage.error("识别失败");
    loading.value = false;
  }
};

const mSubmitRecognition = async (value: string) => {
  if (!fileList.value.length) {
    ElMessage.error("请选择图片");
    return;
  }
  const file = fileList.value[0];

  try {
    const formData = new FormData();
    formData.append("image", file, file.name);
    formData.append("input_text", imgText.value || "请描述图片内容"); // 默认值
    formData.append("mode", value);
    //添加一个加载动画
    loading.value = true;

    const res = await axios.post(app.ocr_api + "/image_api", formData);

    // 假设服务器返回的数据结构是 JSON 对象
    if (res.data.error) {
      ElMessage.error(`识别失败: ${res.data.error}`);
      loading.value = false;
      return;
    }

    recognizedText.value = res.data.recognized_text;
    ElMessage.success("识别成功");
    loading.value = false;
    // console.log("Response from server:", res);
    console.log("recognizedText:", recognizedText.value);
  } catch (error) {
    // ElMessage.error("识别失败");
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.preview-image {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  border: 1px solid #dcdcdc;
  margin-top: 10px;
}
.upload-footer {
  // margin-top: 10px;
  // margin-left: 570px;
  position: relative;
  top: 10px;
  left: 570px;
  margin-bottom: 20px;
}
.upload-demo {
  margin-bottom: 20px;
}
.avatar-uploader {
  :deep() {
    .avatar {
      width: 100%;
      height: 100%;
      display: block;
    }
    .el-upload {
      border: 2px dashed var(--el-color-primary);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      width: 400px;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-upload:hover {
      border-color: var(--el-color-primary);
    }
    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 275px;
      text-align: center;
    }
  }
}

.upload-message {
  width: 110px;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
  border: 2px solid #409eff;
  border-radius: 8px;
  padding: 10px;
}
.section-header {
  display: flex;
  align-items: center;
  margin-top: 20px;
  font-size: 18px;
  color: #333;

  .input-tip {
    width: 5px;
    height: 20px;
    background-color: #409eff;
    margin-right: 10px;
  }
}
.submit-btn {
  margin-top: 450px;
  position: relative;
  left: 1025px;
  z-index: 999;
}
.image-text-recognition{
  height: 650px;
}
</style>